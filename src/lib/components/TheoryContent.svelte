<script lang="ts">
	 
	import { marked } from 'marked';

	export let theoryPath: string;

	let markdownHtml = '<p>Loading theory content...</p>';

	// Load theory content from markdown file
	async function loadTheoryContent(path: string) {
		try {
			const response = await fetch(path);
			if (!response.ok) {
				throw new Error(`Could not fetch theory content: ${response.statusText}`);
			}
			const markdownText = await response.text();
			markdownHtml = await marked.parse(markdownText);
		} catch (error) {
			console.error(error);
			markdownHtml = '<p>Could not load the theory content. Please try again later.</p>';
		}
	}

	// Load content when theoryPath changes
	$: {
		if (theoryPath) {
			loadTheoryContent(theoryPath);
		}
	}
</script>

<div class="theory-content-wrapper">
	 {@html markdownHtml}
</div>

<style>
	.theory-content-wrapper {
		color: #abb2bf;
		line-height: 1.6;
	}

	.theory-content-wrapper :global(h1) {
		color: #61afef;
		font-size: 2rem;
		margin: 2rem 0 1rem 0;
		border-bottom: 2px solid #444;
		padding-bottom: 0.5rem;
	}

	.theory-content-wrapper :global(h2) {
		color: #98c379;
		font-size: 1.5rem;
		margin: 1.5rem 0 1rem 0;
	}

	.theory-content-wrapper :global(h3) {
		color: #e5c07b;
		font-size: 1.2rem;
		margin: 1rem 0 0.5rem 0;
	}

	.theory-content-wrapper :global(h4) {
		color: #c678dd;
		font-size: 1rem;
		margin: 1rem 0 0.5rem 0;
	}

	.theory-content-wrapper :global(p) {
		margin: 1rem 0;
		text-align: justify;
	}

	.theory-content-wrapper :global(ul),
	.theory-content-wrapper :global(ol) {
		margin: 1rem 0;
		padding-left: 2rem;
	}

	.theory-content-wrapper :global(li) {
		margin: 0.5rem 0;
	}

	.theory-content-wrapper :global(code) {
		background-color: #3a3f4b;
		color: #e06c75;
		padding: 0.2rem 0.4rem;
		border-radius: 3px;
		font-family: 'Courier New', monospace;
	}

	.theory-content-wrapper :global(pre) {
		background-color: #000000;
		border: 1px solid #444;
		border-radius: 6px;
		padding: 1rem;
		overflow-x: auto;
		margin: 1rem 0;
	}

	.theory-content-wrapper :global(pre code) {
		background: none;
		padding: 0;
		color: #abb2bf;
	}

	.theory-content-wrapper :global(blockquote) {
		border-left: 4px solid #61afef;
		margin: 1rem 0;
		padding: 0.5rem 1rem;
		background-color: #161b22;
		border-radius: 0 6px 6px 0;
	}

	.theory-content-wrapper :global(table) {
		border-collapse: collapse;
		width: 100%;
		margin: 1rem 0;
		background-color: #161b22;
		border-radius: 6px;
		overflow: hidden;
	}

	.theory-content-wrapper :global(th),
	.theory-content-wrapper :global(td) {
		border: 1px solid #444;
		padding: 0.75rem;
		text-align: left;
	}

	.theory-content-wrapper :global(th) {
		background-color: #3a3f4b;
		color: #61afef;
		font-weight: bold;
	}

	.theory-content-wrapper :global(tr:nth-child(even)) {
		background-color: #0d1117;
	}

	.theory-content-wrapper :global(a) {
		color: #61afef;
		text-decoration: underline;
	}

	.theory-content-wrapper :global(a:hover) {
		color: #98c379;
	}

	.theory-content-wrapper :global(strong) {
		color: #e5c07b;
		font-weight: bold;
	}

	.theory-content-wrapper :global(em) {
		color: #c678dd;
		font-style: italic;
	}

	.theory-content-wrapper :global(hr) {
		border: none;
		border-top: 2px solid #444;
		margin: 2rem 0;
	}

	/* Special styling for theory-specific elements */
	.theory-content-wrapper :global(.highlight) {
		background-color: #e5c07b;
		color: #282c34;
		padding: 0.2rem 0.4rem;
		border-radius: 3px;
		font-weight: bold;
	}

	.theory-content-wrapper :global(.note) {
		background-color: #161b22;
		border-left: 4px solid #98c379;
		padding: 1rem;
		margin: 1rem 0;
		border-radius: 0 6px 6px 0;
	}

	.theory-content-wrapper :global(.warning) {
		background-color: #161b22;
		border-left: 4px solid #e06c75;
		padding: 1rem;
		margin: 1rem 0;
		border-radius: 0 6px 6px 0;
	}
</style>
