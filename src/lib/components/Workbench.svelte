<script lang="ts">
    import { get } from 'svelte/store'
    import { simulation, selectedGateIds, challengeStore } from '$lib/store';
    import type { Gate } from '$lib/logic/types';
     
    import { select } from 'd3-selection';

    // D3ZoomEvent is the key here
    import { zoom, zoomIdentity, type D3ZoomEvent, type ZoomTransform } from 'd3-zoom';

    // --- MODIFICATION 1: Store the transform object ---
    let transform: ZoomTransform = zoomIdentity; // Store the object, not a string

    let drawingWire: { fromPinId: string; x2: number; y2: number } | null = null;
    let svgElement: SVGSVGElement;
    let movingGate: { id: string, startX: number, startY: number } | null = null;

    let selectionBox: { x1: number; y1: number; x2: number; y2: number } | null = null;
    let isMarqueeSelecting = false;

    let isSpacebarPressed = false;

    // Track mouse movement for drag detection
    let mouseDownPosition: { x: number; y: number } | null = null;
    let hasDragged = false;
    let draggedInThisSession = false; // Persists until next mousedown



    function getMousePos(event: MouseEvent) {
        const CTM = svgElement.getScreenCTM();
        if (!CTM) return { x: 0, y: 0 };
        return {
            x: (event.clientX - CTM.e) / CTM.a,
            y: (event.clientY - CTM.f) / CTM.d
        };
    }

    function handlePinMouseDown(event: CustomEvent<{ pinId: string }>) {
		if (movingGate) return;
        
        // Use the same transformed coordinates for the start of the wire
        const startCoords = simulation.getPinCoords(event.detail.pinId);
		drawingWire = {
			fromPinId: event.detail.pinId,
			x2: startCoords.x,
			y2: startCoords.y
		};
	}

    function handleMouseMove(event: MouseEvent) {
        // Check for drag detection if we have a mouse down position
        if (mouseDownPosition && !hasDragged) {
            const { x, y } = getMousePos(event);
            const dragDistance = Math.sqrt(
                Math.pow(x - mouseDownPosition.x, 2) + Math.pow(y - mouseDownPosition.y, 2)
            );
            // Consider it a drag if mouse moved more than 5 pixels
            if (dragDistance > 5) {
                hasDragged = true;
                draggedInThisSession = true; // Mark that dragging occurred in this session
            }
        }

        // Handle marquee selection first
        if (isMarqueeSelecting && selectionBox) {
            // Prevent D3 zoom from interfering
            event.stopPropagation();

            const mousePos = getMousePos(event);
            const [endX, endY] = transform.invert([mousePos.x, mousePos.y]);
            selectionBox.x2 = endX;
            selectionBox.y2 = endY;
            // Trigger a re-render by re-assigning the object
            selectionBox = selectionBox;
            return; // Don't do other move logic while marquee selecting
        }

        const { x: mouseX, y: mouseY } = getMousePos(event); // Get raw mouse coords


        if (drawingWire) {
            // --- THE FIX ---
            // Use d3's invert function to transform the viewport mouse coordinates
            // into the coordinate system of the zoomed/panned <g> element.
            const [transformedX, transformedY] = transform.invert([mouseX, mouseY]);

			drawingWire.x2 = transformedX;
			drawingWire.y2 = transformedY;
            // --- END FIX ---
		}
        // Handle gate moving
        if (movingGate) {
			const { x: mouseX, y: mouseY } = getMousePos(event);
			const dx = (mouseX - movingGate.startX) / transform.k;
			const dy = (mouseY - movingGate.startY) / transform.k;

			movingGate.startX = mouseX;
			movingGate.startY = mouseY;

            // --- MODIFICATION: Move all selected gates ---
            const idsToMove = get(selectedGateIds);
            idsToMove.forEach(id => {
                // We'll reuse the single-gate move logic.
                // This isn't the most performant way (multiple store updates),
                // but it's the simplest to implement.
                simulation.moveGate(id, dx, dy);
            });
    }
    }

    function handlePinMouseUp(event: CustomEvent<{ pinId: string }>) {
        if (!drawingWire) return;
        simulation.addWire(drawingWire.fromPinId, event.detail.pinId);
        drawingWire = null;
        challengeStore.saveCurrentChallengeState();
    }

    function handleGateMouseDown(event: MouseEvent, gate: Gate) {
        event.stopPropagation();
        const currentlySelected = new Set(get(selectedGateIds));

        // Track initial mouse position for drag detection and reset session flag
        const { x, y } = getMousePos(event);
        mouseDownPosition = { x, y };
        hasDragged = false;
        draggedInThisSession = false; // Reset for new interaction

        // If Shift is pressed, add/remove from selection
        if (event.shiftKey) {
            if (currentlySelected.has(gate.id)) {
                currentlySelected.delete(gate.id);
            } else {
                currentlySelected.add(gate.id);
            }
            selectedGateIds.set([...currentlySelected]);
        } else {
            // If not holding shift and clicking an unselected gate, select only that one.
            if (!currentlySelected.has(gate.id)) {
                selectedGateIds.set([gate.id]);
            }
            // If clicking a currently selected gate (without shift), we prepare to move the whole group.
        }

        // Prepare to move the entire selection
		movingGate = { id: gate.id, startX: x, startY: y }; // We still track the primary gate being dragged
    }

    function handleWorkbenchMouseUp(event: MouseEvent) {
        if (isMarqueeSelecting && selectionBox) {
            // Prevent D3 zoom from interfering
            event.stopPropagation();

            // --- Collision Detection Logic ---
            const box = getNormalizedSelectionBox();
            const allGates = [...get(simulation).gates.values()];
            const newSelections: string[] = [];

            for (const gate of allGates) {
                // Simple AABB (Axis-Aligned Bounding Box) collision check
                const gateWidth = 70; // Updated dimensions
                const gateHeight = 90; // Updated dimensions
                const gateRight = gate.x + gateWidth;
                const gateBottom = gate.y + gateHeight;

                if (gate.x < box.x2 && gateRight > box.x1 && gate.y < box.y2 && gateBottom > box.y1) {
                    newSelections.push(gate.id);
                }
            }

            // Update the global store
            selectedGateIds.update(current => {
                const combined = new Set([...current, ...newSelections]);
                return [...combined];
            });
        }

        // Clean up selection state
        isMarqueeSelecting = false;
        selectionBox = null;

        // Reset drag tracking (but keep draggedInThisSession until next mousedown)
        mouseDownPosition = null;
        hasDragged = false;

        // This is called when the mouse is released over the background OR a gate
        movingGate = null; // Stop moving
        drawingWire = null; // Stop drawing wire
        challengeStore.saveCurrentChallengeState();
    }

    function getNormalizedSelectionBox() {
		if (!selectionBox) return { x1: 0, y1: 0, x2: 0, y2: 0 };
		return {
			x1: Math.min(selectionBox.x1, selectionBox.x2),
			y1: Math.min(selectionBox.y1, selectionBox.y2),
			x2: Math.max(selectionBox.x1, selectionBox.x2),
			y2: Math.max(selectionBox.y1, selectionBox.y2),
		};
	}

    function handleWorkbenchMouseDown(event: MouseEvent) {
        // Only handle left mouse button
        if (event.button !== 0) return;

        // Don't start marquee if we're panning (spacebar pressed or middle mouse)
        if (isSpacebarPressed) return;

        // Reset drag session flag for background clicks too
        draggedInThisSession = false;

        // Prevent D3 zoom from interfering
        event.stopPropagation();

        const mousePos = getMousePos(event);
        const [startX, startY] = transform.invert([mousePos.x, mousePos.y]);
        selectionBox = { x1: startX, y1: startY, x2: startX, y2: startY };
        isMarqueeSelecting = true;

        // If the user is not holding shift, clear the previous selection.
        if (!event.shiftKey) {
            selectedGateIds.set([]);
        }
    }

    function cancelDrawing() {
        drawingWire = null;
    }

    function handleSwitchClick(gateId: string) {
        // Only toggle if we haven't dragged in this session
        if (!draggedInThisSession) {
            simulation.toggleSwitch(gateId);
        }
    }

    function handleKeyDown(event: KeyboardEvent) {
		if (event.key === ' ') {
			// Prevent default browser action for spacebar (e.g., scrolling page)
			event.preventDefault();
			isSpacebarPressed = true;
			// Optionally change the cursor to 'grabbing' for better UX
			svgElement.classList.add('panning');
		}

		if (event.key === 'Delete' || event.key === 'Backspace') {
			const idsToDelete = get(selectedGateIds);
			if (idsToDelete.length > 0) {
				event.preventDefault();
				idsToDelete.forEach((id) => simulation.removeGate(id));
				selectedGateIds.set([]);
				challengeStore.saveCurrentChallengeState();
			}
		}
	}

    function panzoom(node: SVGSVGElement) {
		const zoomBehavior = zoom<SVGSVGElement, unknown>()
			.scaleExtent([0.5, 3])
			// === THE FIX: Configure the event filter ===
			.filter((event: MouseEvent | WheelEvent) => {
				// If spacebar is pressed, allow panning with any mouse button.
				if (isSpacebarPressed) return true;

				// Allow all wheel events (for zoom and trackpad pan/scroll).
				if (event.type === 'wheel') return true;

				// Allow panning with the middle mouse button (button === 1).
				if (event.type === 'mousedown' && event.button === 1) return true;

				// Disallow all other events, specifically blocking left-click (button === 0) drag from panning.
				return false;
			})
			.on('zoom', (event: D3ZoomEvent<SVGSVGElement, unknown>) => {
				transform = event.transform;
			});

		select(node).call(zoomBehavior);
	}
    function handleKeyUp(event: KeyboardEvent) {
		if (event.key === ' ') {
			isSpacebarPressed = false;
			svgElement.classList.remove('panning');
		}
	}

    // Reactive properties for drawing wires
    $: startCoords = drawingWire ? simulation.getPinCoords(drawingWire.fromPinId) : { x: 0, y: 0 };
</script>

<svelte:window on:keydown={handleKeyDown} on:keyup={handleKeyUp} />

<svg
    bind:this={svgElement}
    class="workbench"
    class:selecting={isMarqueeSelecting}
    on:mousemove={handleMouseMove}
    on:mouseup={handleWorkbenchMouseUp}
    on:mousedown={handleWorkbenchMouseDown}
    use:panzoom
>
    <defs>
        <!-- Minor grid pattern (every 20px) -->
        <pattern id="minorGrid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#3a3f4b" stroke-width="0.5"/>
        </pattern>

        <!-- Major grid pattern (every 100px) with minor grid background -->
        <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
            <!-- Use the minor grid as the background -->
            <rect width="100" height="100" fill="url(#minorGrid)"/>
            <!-- Add the major grid lines -->
            <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#5c6370" stroke-width="1"/>
        </pattern>
    </defs>

    <!-- NEW: Add a <rect> to act as the background and apply the pattern -->
    

    <!-- === END NEW === -->
    <g transform={transform.toString()}>
        <rect class="grid-background" x="-10000" y="-10000" width="20000" height="20000" fill="url(#grid)" />
        <!-- Render Wires -->
        {#each [...$simulation.wires.values()] as wire (wire.id)}
            {@const from = simulation.getPinCoords(wire.fromPinId)}
            {@const to = simulation.getPinCoords(wire.toPinId)}
            {@const fromPin = $simulation.pins.get(wire.fromPinId)}
            <line x1={from.x} y1={from.y} x2={to.x} y2={to.y} class:active={fromPin?.value === 1} />
        {/each}

        <!-- Render Ghost Wire while drawing -->
        {#if drawingWire}
            <!-- The coordinates for the line are now all in the same (transformed) system -->
            <line x1={startCoords.x} y1={startCoords.y} x2={drawingWire.x2} y2={drawingWire.y2} class="ghost" />
        {/if}

        <!-- Render Gates -->
        {#each [...$simulation.gates.values()] as gate (gate.id)}
            <g
                transform="translate({gate.x}, {gate.y})"
                class="gate-group"
                class:selected={$selectedGateIds.includes(gate.id)}
                on:mousedown|stopPropagation={(e) => handleGateMouseDown(e, gate)}
            >
                <!-- Gate Body -->
                {#if gate.type === 'LED'}
                    {@const inputPin = $simulation.pins.get(gate.inputs[0].id)}
                    <!-- LED: Circular bulb with glow effect (scaled up) -->
                    <circle cx="35" cy="45" r="25" class="led-outer" class:active={inputPin?.value === 1} />
                    <circle cx="35" cy="45" r="17" class="led-inner" class:active={inputPin?.value === 1} />
                    <!-- LED rays when active -->
                    {#if inputPin?.value === 1}
                        <g class="led-rays">
                            <line x1="35" y1="12" x2="35" y2="18" stroke="#e06c75" stroke-width="2"/>
                            <line x1="35" y1="72" x2="35" y2="78" stroke="#e06c75" stroke-width="2"/>
                            <line x1="8" y1="45" x2="14" y2="45" stroke="#e06c75" stroke-width="2"/>
                            <line x1="56" y1="45" x2="62" y2="45" stroke="#e06c75" stroke-width="2"/>
                            <line x1="18" y1="26" x2="22" y2="30" stroke="#e06c75" stroke-width="2"/>
                            <line x1="48" y1="60" x2="52" y2="64" stroke="#e06c75" stroke-width="2"/>
                            <line x1="52" y1="26" x2="48" y2="30" stroke="#e06c75" stroke-width="2"/>
                            <line x1="22" y1="60" x2="18" y2="64" stroke="#e06c75" stroke-width="2"/>
                        </g>
                    {/if}
                {:else if gate.type === 'SWITCH'}
                    {@const outputPin = $simulation.pins.get(gate.outputs[0].id)}
                    <!-- Switch: Toggle switch appearance (scaled up) -->
                    <rect x="10" y="30" width="50" height="30" rx="15" class="switch-body"/>
                    <circle cx={outputPin?.value === 1 ? "50" : "20"} cy="45" r="12" class="switch-toggle" class:active={outputPin?.value === 1}/>
                    <text x="35" y="80" text-anchor="middle" class="gate-label">SWITCH</text>
                {:else if gate.type === 'AND'}
                    <!-- AND Gate: D-shaped (scaled up) -->
                    <path d="M 10 20 L 35 20 Q 55 20 55 45 Q 55 70 35 70 L 10 70 Z" class="gate-body"/>
                    <text x="30" y="50" text-anchor="middle" class="gate-label">AND</text>
                {:else if gate.type === 'NAND'}
                    <!-- NAND Gate: D-shaped with output bubble (scaled up) -->
                    <path d="M 10 20 L 35 20 Q 55 20 55 45 Q 55 70 35 70 L 10 70 Z" class="gate-body"/>
                    <!-- Output bubble that serves as both visual indicator and interactive pin -->
                    <circle cx="60" cy="45" r="5" class="gate-bubble pin output"
                            class:active={gate.outputs[0]?.value === 1}
                            on:mousedown|stopPropagation={(e) => handlePinMouseDown(new CustomEvent('mousedown', { detail: { pinId: gate.outputs[0].id } }))}/>
                    <text x="30" y="50" text-anchor="middle" class="gate-label">NAND</text>

                {:else if gate.type === 'CLOCK'}
                    {@const outputPin = $simulation.pins.get(gate.outputs[0].id)}
                    <!-- Clock: Circular shape with clock symbol -->
                    <circle cx="35" cy="45" r="30" class="clock-body" class:active={outputPin?.value === 1}/>
                    <!-- Clock symbol - square wave -->
                    <path d="M 15 45 L 20 45 L 20 35 L 30 35 L 30 55 L 40 55 L 40 35 L 50 35 L 50 45 L 55 45"
                          fill="none" stroke="#abb2bf" stroke-width="2" class="clock-symbol"/>
                    <text x="35" y="80" text-anchor="middle" class="gate-label">CLOCK</text>
                {:else if gate.type === 'OR'}
                    <!-- OR Gate: Curved shape -->
                    <path d="M 10 20 Q 25 20 35 45 Q 25 70 10 70 Q 30 45 10 20" class="gate-body"/>
                    <text x="25" y="50" text-anchor="middle" class="gate-label">OR</text>
                {:else if gate.type === 'NOT'}
                    <!-- NOT Gate: Triangle with bubble -->
                    <path d="M 10 25 L 10 65 L 50 45 Z" class="gate-body"/>
                    <!-- Output bubble that serves as both visual indicator and interactive pin -->
                    <circle cx="55" cy="45" r="5" class="gate-bubble pin output"
                            class:active={gate.outputs[0]?.value === 1}
                            on:mousedown|stopPropagation={(e) => handlePinMouseDown(new CustomEvent('mousedown', { detail: { pinId: gate.outputs[0].id } }))}/>
                    <text x="30" y="80" text-anchor="middle" class="gate-label">NOT</text>
                {:else if gate.type === 'D-FLIP-FLOP'}
                    <!-- D Flip-Flop: Rectangle with clock triangle -->
                    <rect width="80" height="60" rx="5" class="gate-body"/>
                    <!-- Clock triangle indicator -->
                    <path d="M 0 50 L 10 45 L 0 40" stroke="#61dafb" stroke-width="2" fill="none"/>
                    <text x="40" y="50" text-anchor="middle" class="gate-label">D-FF</text>
                {:else if gate.type === '4BIT-REGISTER' || gate.type === '8BIT-REGISTER'}
                    <!-- Register: Wide rectangle -->
                    <rect width="100" height="70" rx="5" class="gate-body"/>
                    <!-- Clock triangle indicator -->
                    <path d="M 0 60 L 10 55 L 0 50" stroke="#61dafb" stroke-width="2" fill="none"/>
                    <text x="50" y="45" text-anchor="middle" class="gate-label">{gate.type.replace('BIT-', '')}</text>
                {:else if gate.type === '2TO4-DECODER'}
                    <!-- Decoder: Triangular shape -->
                    <path d="M 10 20 L 70 35 L 70 55 L 10 70 Z" class="gate-body"/>
                    <text x="40" y="50" text-anchor="middle" class="gate-label">2:4 DEC</text>
                {:else if gate.type === '4X4-RAM'}
                    <!-- RAM: Memory block -->
                    <rect width="90" height="80" rx="5" class="gate-body"/>
                    <!-- Memory grid pattern -->
                    <line x1="20" y1="20" x2="20" y2="70" stroke="#61dafb" stroke-width="1"/>
                    <line x1="70" y1="20" x2="70" y2="70" stroke="#61dafb" stroke-width="1"/>
                    <line x1="20" y1="35" x2="70" y2="35" stroke="#61dafb" stroke-width="1"/>
                    <line x1="20" y1="55" x2="70" y2="55" stroke="#61dafb" stroke-width="1"/>
                    <text x="45" y="50" text-anchor="middle" class="gate-label">4×4 RAM</text>
                {:else if gate.type === 'FULL-ADDER'}
                    <!-- Full Adder: Rectangle with + symbol -->
                    <rect width="80" height="70" rx="5" class="gate-body"/>
                    <text x="40" y="40" text-anchor="middle" class="gate-label" font-size="24">+</text>
                    <text x="40" y="60" text-anchor="middle" class="gate-label" font-size="10">FULL</text>
                {:else if gate.type === '2BIT-ADDER'}
                    <!-- 2-bit Adder: Wide rectangle with + symbol -->
                    <rect width="100" height="80" rx="5" class="gate-body"/>
                    <text x="50" y="45" text-anchor="middle" class="gate-label" font-size="24">+</text>
                    <text x="50" y="65" text-anchor="middle" class="gate-label" font-size="10">2-BIT</text>
                {:else if gate.type === 'SR-LATCH'}
                    <!-- SR Latch: Rectangle with S/R labels -->
                    <rect width="70" height="60" rx="5" class="gate-body"/>
                    <text x="35" y="40" text-anchor="middle" class="gate-label">SR</text>
                    <text x="35" y="55" text-anchor="middle" class="gate-label" font-size="10">LATCH</text>
                {:else}
                    <!-- Default rectangular gate for other types (scaled up) -->
                    <rect width="70" height="90" rx="5" class="gate-body"/>
                    <text x="35" y="50" text-anchor="middle" class="gate-label">{gate.type}</text>
                {/if}

                <!-- Gate Pins -->
                {#each gate.inputs as pin, i (pin.id)}
                    {@const pinY = gate.type === 'LED' ? 45 :
                                   gate.type === 'NOT' ? 45 :
                                   gate.type === 'D-FLIP-FLOP' ? (i === 0 ? 35 : 55) :
                                   gate.type === '4BIT-REGISTER' ? (i < 4 ? 25 + i * 10 : (i === 4 ? 55 : 65)) :
                                   gate.type === '8BIT-REGISTER' ? (i < 8 ? 20 + i * 6 : (i === 8 ? 55 : 65)) :
                                   gate.type === '2TO4-DECODER' ? (i < 2 ? 30 + i * 15 : 60) :
                                   gate.type === '4X4-RAM' ? (i < 2 ? 25 + i * 15 : i < 6 ? 20 + (i-2) * 10 : (i === 6 ? 65 : 75)) :
                                   gate.type === 'FULL-ADDER' ? 25 + i * 15 :
                                   gate.type === '2BIT-ADDER' ? 20 + i * 12 :
                                   gate.type === 'SR-LATCH' ? (i === 0 ? 35 : 55) :
                                   35 + i * 20}
                    <circle
                        cx="-5"
                        cy={pinY}
                        r="5"
                        class="pin input"
                        on:mouseup|stopPropagation={(e) => handlePinMouseUp(new CustomEvent('mouseup', { detail: { pinId: pin.id } }))}
                    />
                    {#if gate.type === 'D-LATCH'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i === 0 ? 'D' : 'E'}
                        </text>
                    {:else if gate.type === 'D-FLIP-FLOP'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i === 0 ? 'D' : 'CLK'}
                        </text>
                    {:else if gate.type === '4BIT-REGISTER' || gate.type === '8BIT-REGISTER'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i < (gate.type === '4BIT-REGISTER' ? 4 : 8) ? `D${i}` : (i === (gate.type === '4BIT-REGISTER' ? 4 : 8) ? 'CLK' : 'LD')}
                        </text>
                    {:else if gate.type === '2TO4-DECODER'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i < 2 ? `A${i}` : 'EN'}
                        </text>
                    {:else if gate.type === '4X4-RAM'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i < 2 ? `A${i}` : i < 6 ? `D${i-2}` : (i === 6 ? 'WE' : 'CLK')}
                        </text>
                    {:else if gate.type === 'FULL-ADDER'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i === 0 ? 'A' : i === 1 ? 'B' : 'Cin'}
                        </text>
                    {:else if gate.type === '2BIT-ADDER'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i === 0 ? 'A1' : i === 1 ? 'A0' : i === 2 ? 'B1' : i === 3 ? 'B0' : 'Cin'}
                        </text>
                    {:else if gate.type === 'SR-LATCH'}
                        <text class="pin-label" x="-15" y={pinY + 4} text-anchor="end">
                            {i === 0 ? 'S' : 'R'}
                        </text>
                    {/if}
                {/each}

                <!-- Output pins - skip for NAND and NOT gates as they use the bubble -->
                {#if gate.type !== 'NAND' && gate.type !== 'NOT'}
                    {#each gate.outputs as pin, i (pin.id)}
                        {@const pinY = gate.type === 'D-FLIP-FLOP' ? (i === 0 ? 35 : 55) :
                                       gate.type === '4BIT-REGISTER' ? 25 + i * 10 :
                                       gate.type === '8BIT-REGISTER' ? 20 + i * 6 :
                                       gate.type === '2TO4-DECODER' ? 30 + i * 10 :
                                       gate.type === '4X4-RAM' ? 20 + i * 10 :
                                       45}
                        {@const pinX = gate.type === '4BIT-REGISTER' || gate.type === '8BIT-REGISTER' ? 105 :
                                       gate.type === '2TO4-DECODER' ? 75 :
                                       gate.type === '4X4-RAM' ? 95 :
                                       gate.type === 'D-FLIP-FLOP' ? 85 :
                                       75}
                        <circle
                            cx={pinX}
                            cy={pinY}
                            r="5"
                            class="pin output"
                            class:active={pin.value === 1}
                            on:mousedown|stopPropagation={(e) => handlePinMouseDown(new CustomEvent('mousedown', { detail: { pinId: pin.id } }))}
                        />
                        {#if gate.type === 'D-LATCH'}
                            <text class="pin-label" x="85" y={pinY + 4} text-anchor="start">
                                {i === 0 ? 'Q' : ''}
                            </text>
                        {:else if gate.type === 'D-FLIP-FLOP'}
                            <text class="pin-label" x="95" y={pinY + 4} text-anchor="start">
                                {i === 0 ? 'Q' : 'Q̄'}
                            </text>
                        {:else if gate.type === '4BIT-REGISTER' || gate.type === '8BIT-REGISTER'}
                            <text class="pin-label" x="115" y={pinY + 4} text-anchor="start">
                                Q{i}
                            </text>
                        {:else if gate.type === '2TO4-DECODER'}
                            <text class="pin-label" x="85" y={pinY + 4} text-anchor="start">
                                Y{i}
                            </text>
                        {:else if gate.type === '4X4-RAM'}
                            <text class="pin-label" x="105" y={pinY + 4} text-anchor="start">
                                Q{i}
                            </text>
                        {:else if gate.type === 'FULL-ADDER'}
                            <text class="pin-label" x="85" y={pinY + 4} text-anchor="start">
                                {i === 0 ? 'Sum' : 'Cout'}
                            </text>
                        {:else if gate.type === '2BIT-ADDER'}
                            <text class="pin-label" x="105" y={pinY + 4} text-anchor="start">
                                {i === 0 ? 'S1' : i === 1 ? 'S0' : 'Cout'}
                            </text>
                        {:else if gate.type === 'SR-LATCH'}
                            <text class="pin-label" x="85" y={pinY + 4} text-anchor="start">
                                {i === 0 ? 'Q' : 'Q̄'}
                            </text>
                        {/if}

                    {/each}
                {/if}

                <!-- Clickable area for switch only -->
                {#if gate.type === 'SWITCH'}
                    <rect
                        width="70"
                        height="90"
                        fill="transparent"
                        cursor="pointer"
                        on:click={() => handleSwitchClick(gate.id)}
                    />
                {/if}
            </g>
        {/each}

        <!-- NEW: Render the Selection Box -->
        {#if selectionBox}
            {@const box = getNormalizedSelectionBox()}
            <rect 
                class="selection-box"
                x={box.x1}
                y={box.y1}
                width={box.x2 - box.x1}
                height={box.y2 - box.y1}
            />
        {/if}
    </g>
</svg>

<style>
    .workbench {
        width: 100%;
        height: 100%;
        border: 1px solid var(--border-default);
        background-color: var(--bg-primary);
        user-select: none;
        border-radius: var(--radius-sm);
    }
    .gate-body {
        fill: var(--bg-surface);
        stroke: var(--color-accent);
        stroke-width: 2;
        transition: all var(--transition-fast);
    }
    .gate-bubble {
        fill: var(--bg-primary);
        stroke: var(--color-accent);
        stroke-width: 2;
        cursor: crosshair;
        transition: all var(--transition-fast);
    }
    .gate-bubble.active {
        fill: var(--color-success);
        stroke: var(--color-success);
    }
    .gate-label {
        fill: var(--text-primary);
        font-family: var(--font-family-mono);
        font-size: 10px;
        font-weight: var(--font-weight-semibold);
        pointer-events: none;
    }
    .pin {
        fill: var(--text-muted);
        stroke: var(--text-secondary);
        stroke-width: 1;
        cursor: crosshair;
        transition: all var(--transition-fast);
    }
    .pin.active {
        fill: var(--color-success);
        stroke: var(--color-success);
    }
    line {
        stroke: var(--text-muted);
        stroke-width: 3;
        pointer-events: none;
        transition: all var(--transition-fast);
    }
    line.active {
        stroke: var(--color-success);
        stroke-dasharray: 1;
        animation: dash 2s linear infinite;
    }

    @keyframes dash {
        to {
            stroke-dashoffset: -20;
        }
    }
    line.ghost {
        stroke: var(--color-accent);
        stroke-dasharray: 5 5;
        opacity: 0.7;
    }
    /* LED Styles */
    .led-outer {
        fill: var(--bg-surface);
        stroke: var(--color-error);
        stroke-width: 2;
        transition: all var(--transition-normal);
    }
    .led-outer.active {
        fill: var(--color-error);
        filter: drop-shadow(0 0 12px var(--color-error));
    }
    .led-inner {
        fill: var(--bg-tertiary);
        stroke: none;
        transition: all var(--transition-normal);
    }
    .led-inner.active {
        fill: var(--color-error-hover);
    }
    .led-rays {
        opacity: 0.9;
        stroke: var(--color-error);
    }

    /* Switch Styles */
    .switch-body {
        fill: #3a3f4b;
        stroke: #61afef;
        stroke-width: 2;
    }
    .switch-toggle {
        fill: #5c6370;
        stroke: #abb2bf;
        stroke-width: 2;
        transition: all 0.2s;
    }
    .switch-toggle.active {
        fill: #98c379;
        stroke: #98c379;
    }

    /* Clock Styles */
    .clock-body {
        fill: #3a3f4b;
        stroke: #61afef;
        stroke-width: 2;
        transition: all 0.2s;
    }
    .clock-body.active {
        fill: #61afef;
        filter: drop-shadow(0 0 6px #61afef);
    }
    .clock-symbol {
        pointer-events: none;
    }
    .gate-group {
        cursor: grab;
    }
    .gate-group:active {
        cursor: grabbing;
    }
    .gate-group.selected > .gate-body {
        stroke: #c678dd; /* Purple selection highlight */
        stroke-width: 3;
    }
    .gate-group.selected > .led-outer {
        stroke: #c678dd; /* Purple selection highlight */
        stroke-width: 3;
    }
    .gate-group.selected > .switch-body {
        stroke: #c678dd; /* Purple selection highlight */
        stroke-width: 3;
    }
    .gate-group.selected > .gate-bubble {
        stroke: #c678dd; /* Purple selection highlight */
        stroke-width: 3;
    }
    .gate-group.selected > .clock-body {
        stroke: #c678dd; /* Purple selection highlight */
        stroke-width: 3;
    }
    .gate-group.black-box > .gate-body {
        stroke: #98c379; /* Green stroke to signify 'unlocked' */
    }
    .grid-background {
        pointer-events: none; /* Make sure it doesn't interfere with mouse events */
    }
    .pin-label {
        fill: #abb2bf;
        font-family: monospace;
        font-size: 11px;
        pointer-events: none;
    }
    .selection-box {
        fill: rgba(97, 175, 239, 0.2);
        stroke: rgba(97, 175, 239, 0.8);
        stroke-width: 1px;
        stroke-dasharray: 4 2;
    }
    .workbench.panning {
		cursor: grab;
	}
	.workbench.panning:active {
		cursor: grabbing;
	}
	.workbench.selecting {
		cursor: crosshair;
	}
</style>