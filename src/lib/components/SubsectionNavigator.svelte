<script lang="ts">
  import type { TheorySubsection } from '$lib/curriculum';

  export let subsections: TheorySubsection[];
  export let currentSubsectionIndex: number = 0;
  export let onSubsectionChange: (index: number) => void;

  $: canGoBack = currentSubsectionIndex > 0;
  $: canGoForward = currentSubsectionIndex < subsections.length - 1;
  $: currentSubsection = subsections[currentSubsectionIndex];
  $: progress = `${currentSubsectionIndex + 1} of ${subsections.length}`;

  function goToPrevious() {
    if (canGoBack) {
      const newIndex = currentSubsectionIndex - 1;
      onSubsectionChange(newIndex);
    }
  }

  function goToNext() {
    if (canGoForward) {
      const newIndex = currentSubsectionIndex + 1;
      onSubsectionChange(newIndex);
    }
  }






</script>

<nav class="subsection-navigator" aria-label="Subsection navigation">
  <div class="navigator-header">
    <div class="subsection-info">
      <h2 class="subsection-title">{currentSubsection.title}</h2>
      {#if currentSubsection.description}
        <p class="subsection-description">{currentSubsection.description}</p>
      {/if}
      {#if currentSubsection.estimatedTime}
        <div class="estimated-time">
          <span class="time-icon">⏱️</span>
          <span class="time-text">{currentSubsection.estimatedTime}</span>
        </div>
      {/if}
    </div>
    
    <div class="progress-indicator">
      <span class="progress-text">{progress}</span>
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          style="width: {((currentSubsectionIndex + 1) / subsections.length) * 100}%"
        ></div>
      </div>
    </div>
  </div>

  <div class="navigator-controls">
    <button 
      class="nav-button prev-button" 
      class:disabled={!canGoBack}
      on:click={goToPrevious}
      disabled={!canGoBack}
    >
      <span class="nav-icon">←</span>
      <span class="nav-text">Previous</span>
    </button>

    <div class="subsection-dots">
      {#each subsections as subsection, index}
        <button
          class="dot"
          class:active={index === currentSubsectionIndex}
          on:click={() => {
            onSubsectionChange(index);
          }}
          title={subsection.title}
        >
          <span class="dot-number">{index + 1}</span>
        </button>
      {/each}
    </div>

    <button 
      class="nav-button next-button" 
      class:disabled={!canGoForward}
      on:click={goToNext}
      disabled={!canGoForward}
    >
      <span class="nav-text">Next</span>
      <span class="nav-icon">→</span>
    </button>
  </div>
</nav>

<style>
  .subsection-navigator {
    background-color: var(--bg-elevated);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-md);
    padding: var(--spacing-3) var(--spacing-4);
    margin-bottom: var(--spacing-4);
    box-shadow: var(--shadow-xs);
  }

  .navigator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-3);
    gap: var(--spacing-3);
  }

  .subsection-info {
    flex: 1;
  }

  .subsection-title {
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-accent);
    line-height: var(--line-height-tight);
  }

  .subsection-description {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
  }

  .estimated-time {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
  }

  .progress-indicator {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-2);
    min-width: 120px;
  }

  .progress-text {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background-color: var(--bg-surface);
    border-radius: var(--radius-full);
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-accent), var(--color-success));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
  }

  .navigator-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-4);
  }

  .nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-5);
    background-color: var(--bg-elevated);
    color: var(--text-primary);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 100px;
  }

  .nav-button:hover:not(.disabled) {
    background-color: var(--color-accent);
    color: var(--text-inverse);
    border-color: var(--color-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  .nav-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .nav-icon {
    font-size: var(--font-size-base);
  }

  .subsection-dots {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
  }

  .dot {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    border: 2px solid var(--border-default);
    background-color: var(--bg-surface);
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dot:hover {
    border-color: var(--color-accent);
    background-color: var(--color-accent);
    color: var(--text-inverse);
    transform: scale(1.1);
  }

  .dot.active {
    border-color: var(--color-accent);
    background-color: var(--color-accent);
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
  }



  /* Responsive Design */
  @media (max-width: 1024px) {
    .subsection-navigator {
      padding: var(--spacing-4);
    }

    .navigator-header {
      gap: var(--spacing-3);
    }

    .subsection-title {
      font-size: var(--font-size-lg);
    }
  }

  @media (max-width: 768px) {
    .navigator-header {
      flex-direction: column;
      align-items: stretch;
    }

    .progress-indicator {
      align-items: stretch;
    }

    .navigator-controls {
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .subsection-dots {
      justify-content: center;
      flex-wrap: wrap;
      order: -1; /* Show dots above buttons on mobile */
    }

    .nav-button {
      width: 100%;
      justify-content: center;
    }

    .subsection-title {
      font-size: var(--font-size-base);
    }

    .subsection-description {
      font-size: var(--font-size-xs);
    }
  }

  @media (max-width: 480px) {
    .subsection-navigator {
      padding: var(--spacing-3);
      margin-bottom: var(--spacing-4);
    }

    .navigator-header {
      margin-bottom: var(--spacing-4);
    }

    .dot {
      width: 28px;
      height: 28px;
      font-size: 10px;
    }
  }
</style>
