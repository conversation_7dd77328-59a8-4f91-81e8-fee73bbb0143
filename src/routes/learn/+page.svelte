<script lang="ts">
	import Workbench from '$lib/components/Workbench.svelte';
    import StudyPanel from '$lib/components/StudyPanel.svelte';
    import TheoryContent from '$lib/components/TheoryContent.svelte';
	import { simulation, challengeStore, navigationStore } from '$lib/store';
	import type { GateType } from '$lib/logic/types';
    import { curriculum, studyContent, type Challenge } from '$lib/curriculum';
    import { onMount } from 'svelte';


    let panelWidth = 400; // Default width for the panel

    let showSuccessModal = false;
    let completedChallenge: Challenge | null = null;

    // Dropdown state
    let isDropdownOpen = false;
    let dropdownButton: HTMLButtonElement;

	let nextGateX = 50;
	let nextGateY = 50;

    // Reactively get the index and tools directly from the store's VALUE ($)
    $: levelIndex = $challengeStore.levelIndex;
    $: availableTools = $challengeStore.unlockedTools;
    $: currentView = $navigationStore.currentView;
    $: currentChallengeId = $navigationStore.currentChallengeId;
    $: currentTheoryId = $navigationStore.currentTheoryId;
    $: currentModuleId = $navigationStore.currentModuleId;
    $: currentLearningItemId = $navigationStore.currentLearningItemId;

    // Derive the current challenge from the reactive index or navigation
    $: currentChallenge = currentChallengeId
        ? curriculum.find(c => c.id === currentChallengeId) || curriculum[levelIndex]
        : curriculum[levelIndex];

    // Derive current theory content
    $: currentModule = currentModuleId ? studyContent.modules.find(m => m.id === currentModuleId) : null;
    $: currentTheory = currentTheoryId ?
        (currentModule?.theory?.id === currentTheoryId ? currentModule.theory :
         studyContent.theoryIndex.find(t => t.id === currentTheoryId)) : null;
    $: currentLearningItem = currentLearningItemId && currentModule ?
        currentModule.learningItems?.find(item => item.id === currentLearningItemId) : null;

	function add(type: GateType) {
        if (!availableTools.has(type)) return;

		simulation.addGate(type, nextGateX, nextGateY);
		nextGateX += 150;
		if (nextGateX > 500) {
			nextGateX = 50;
			nextGateY += 120;
		}
	}
    
    // Listen for challenge completion events
    function handleChallengeCompleted(event: CustomEvent<{ challenge: Challenge }>) {
        completedChallenge = event.detail.challenge;
        showSuccessModal = true;
    }

    // Set up event listener for challenge completion
    onMount(() => {
        window.addEventListener('challengeCompleted', handleChallengeCompleted);

        return () => {
            window.removeEventListener('challengeCompleted', handleChallengeCompleted);
        };
    });

    function continueAfterSuccess() {
        showSuccessModal = false;
        completedChallenge = null;

        // Navigate back to the module view to show progress
        if ($navigationStore.currentModuleId) {
            navigationStore.goToModule($navigationStore.currentModuleId);
        }
    }



    function resetWorkbench() {
        simulation.reset();
    }

    // Dropdown functionality
    function toggleDropdown() {
        isDropdownOpen = !isDropdownOpen;
    }

    function selectComponent(tool: GateType) {
        add(tool);
        isDropdownOpen = false;
    }

    function closeDropdown() {
        isDropdownOpen = false;
    }

    // Close dropdown when clicking outside
    function handleClickOutside(event: MouseEvent) {
        if (dropdownButton && !dropdownButton.contains(event.target as Node)) {
            closeDropdown();
        }
    }

    // Handle keyboard navigation
    function handleDropdownKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            closeDropdown();
            dropdownButton?.focus();
        }
    }
</script>

<!-- Add window event listener for clicking outside dropdown -->
<svelte:window on:click={handleClickOutside} />

<!-- Enhanced Success Modal -->
{#if showSuccessModal && completedChallenge}
<div class="modal-backdrop">
    <div class="success-modal">
        <div class="modal-header">
            <div class="success-icon">🎉</div>
            <h2>Challenge Complete!</h2>
        </div>

        <div class="modal-content">
            <h3>{completedChallenge.title}</h3>
            <p class="success-message">Excellent work! You've successfully completed this challenge.</p>

            {#if completedChallenge.unlocks}
                <div class="unlock-notification">
                    <div class="unlock-icon">🔓</div>
                    <p class="unlock-msg">
                        <strong>New Tool Unlocked!</strong><br>
                        You can now use the <strong>{completedChallenge.unlocks}</strong> component in future challenges.
                    </p>
                </div>
            {/if}

            <div class="progress-indicator">
                <p>Keep building your 8-bit computer, one component at a time!</p>
            </div>
        </div>

        <div class="modal-actions">
            <button class="continue-button" on:click={continueAfterSuccess}>
                Continue to Challenge Menu
                <span class="button-arrow">→</span>
            </button>
        </div>
    </div>
</div>
{/if}

<div>
    <StudyPanel
        bind:width={panelWidth}
    />
    <main style="margin-left: {panelWidth}px;">
        {#if currentView === 'learning-item' && currentLearningItem}
            {#if currentLearningItem.type === 'challenge' && currentLearningItem.challenge}
                <!-- Learning Item Challenge View -->
                <header>
                    <h1>{currentLearningItem.challenge.title}</h1>
                    <p>{currentLearningItem.challenge.description}</p>

                    <div class="controls">
                        <div class="dropdown-container">
                            <button
                                bind:this={dropdownButton}
                                class="dropdown-button"
                                on:click={toggleDropdown}
                                on:keydown={handleDropdownKeydown}
                                aria-haspopup="listbox"
                                aria-expanded={isDropdownOpen}
                                aria-label="Add component dropdown"
                            >
                                Add Component ▼
                            </button>
                            {#if isDropdownOpen}
                                <div
                                    class="dropdown-menu"
                                    role="listbox"
                                    aria-label="Available components"
                                >
                                    {#each [...availableTools] as tool (tool)}
                                        <button
                                            class="dropdown-item"
                                            role="option"
                                            aria-selected="false"
                                            on:click={() => selectComponent(tool)}
                                            on:keydown={(e) => e.key === 'Enter' && selectComponent(tool)}
                                            aria-label="Add {tool}"
                                        >
                                            {tool}
                                        </button>
                                    {/each}
                                </div>
                            {/if}
                        </div>
                        <button class="reset" on:click={resetWorkbench}>Reset Workbench</button>
                    </div>
                </header>

                <div class="workbench-container">
                    <Workbench />
                </div>
            {:else if currentLearningItem.type === 'theory' && currentLearningItem.content}
                <!-- Learning Item Theory View -->
                <div class="theory-view">
                    <div class="theory-content">
                        <TheoryContent theoryPath={currentLearningItem.content} />
                    </div>
                </div>
            {:else}
                <!-- Other learning item types -->
                <div class="contextual-content">
                    <div class="learning-item-content">
                        <h1>{currentLearningItem.title}</h1>
                        <p>{currentLearningItem.description}</p>
                        {#if currentLearningItem.estimatedTime}
                            <p><strong>⏱️ Estimated time:</strong> {currentLearningItem.estimatedTime}</p>
                        {/if}
                        {#if currentLearningItem.type === 'video' && currentLearningItem.videoUrl}
                            <div class="video-container">
                                <iframe src={currentLearningItem.videoUrl} title={currentLearningItem.title}></iframe>
                            </div>
                        {/if}
                    </div>
                </div>
            {/if}
        {:else if currentView === 'challenge' && currentChallenge}
            <header>
                <h1>{currentChallenge.title}</h1>
                <p>{currentChallenge.description}</p>

                <div class="controls">
                    <div class="dropdown-container">
                        <button
                            bind:this={dropdownButton}
                            class="dropdown-button"
                            on:click={toggleDropdown}
                            on:keydown={handleDropdownKeydown}
                            aria-haspopup="listbox"
                            aria-expanded={isDropdownOpen}
                            aria-label="Add component dropdown"
                        >
                            Add Component ▼
                        </button>
                        {#if isDropdownOpen}
                            <div
                                class="dropdown-menu"
                                role="listbox"
                                aria-label="Available components"
                            >
                                {#each [...availableTools] as tool (tool)}
                                    <button
                                        class="dropdown-item"
                                        role="option"
                                        aria-selected="false"
                                        on:click={() => selectComponent(tool)}
                                        on:keydown={(e) => e.key === 'Enter' && selectComponent(tool)}
                                        aria-label="Add {tool}"
                                    >
                                        {tool}
                                    </button>
                                {/each}
                            </div>
                        {/if}
                    </div>
                    <button class="reset" on:click={resetWorkbench}>Reset Workbench</button>
                </div>


            </header>

            <div class="workbench-container">
                <Workbench />
            </div>
        {:else if currentView === 'theory' && currentTheory}
            <!-- Theory View - Show theory content in main area with StudyPanel visible -->
            <div class="theory-view">
                <div class="theory-content">
                    <TheoryContent theoryPath={currentTheory.content} />
                </div>
            </div>
        {:else}
            <!-- Contextual Content Area -->
            <div class="contextual-content">
                {#if currentView === 'main-menu'}
                    <!-- Learning Hub Overview -->
                    <div class="overview-content">
                        <h1>🎯 Your Learning Journey</h1>
                        <div class="journey-overview">
                            <h2>Building an 8-Bit Computer from Scratch</h2>
                            <p>You're about to embark on an incredible journey through computer architecture. Starting with basic logic gates, you'll progressively build toward a complete, programmable 8-bit computer.</p>

                            <div class="progress-summary">
                                <h3>📊 Your Progress</h3>
                                <div class="progress-stats">
                                    <div class="stat-item">
                                        <span class="stat-number">{$challengeStore.completedChallenges.size}</span>
                                        <span class="stat-label">Challenges Completed</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">{studyContent.modules.length}</span>
                                        <span class="stat-label">Modules Available</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">{$challengeStore.unlockedTools.size}</span>
                                        <span class="stat-label">Tools Unlocked</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {:else if currentView === 'module' && currentModule}
                    <!-- Module Introduction -->
                    <div class="module-intro">
                        <h1>📖 {currentModule.title}</h1>
                        <div class="module-description">
                            <p>{currentModule.description}</p>
                        </div>

                        {#if currentModule.introduction}
                            <div class="module-introduction">
                                <h2>👋 Welcome to This Module</h2>
                                <p>{currentModule.introduction}</p>
                            </div>
                        {/if}

                        <div class="module-learning-objectives">
                            <h2>🎯 What You'll Learn</h2>
                            <p>In this module, you will:</p>
                            <ul>
                                {#if currentModule.learningItems && currentModule.learningItems.length > 0}
                                    <li>Complete {currentModule.learningItems.filter(item => item.type === 'theory').length} theory lessons to understand the concepts</li>
                                    <li>Practice with {currentModule.learningItems.filter(item => item.type === 'challenge').length} hands-on challenges</li>
                                    <li>Build real circuits that demonstrate the principles you've learned</li>
                                {:else}
                                    <li>Study the theoretical foundations through comprehensive theory content</li>
                                    <li>Apply your knowledge through hands-on circuit building challenges</li>
                                    <li>Master the concepts needed for the next stage of computer construction</li>
                                {/if}
                            </ul>
                        </div>

                        <div class="module-approach">
                            <h2>📚 How to Proceed</h2>
                            <div class="approach-steps">
                                <div class="step-item">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h3>Follow the Learning Path</h3>
                                        <p>Work through the learning items in the StudyPanel in order for the best experience</p>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h3>Take Your Time</h3>
                                        <p>Each item has an estimated time - use this to plan your learning sessions</p>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h3>Practice and Experiment</h3>
                                        <p>Don't just complete challenges - experiment with different approaches to deepen understanding</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {:else}
                    <!-- Default fallback -->
                    <div class="default-content">
                        <h1>ByteCrafted Learning Platform</h1>
                        <p>Select a module from the Learning Hub to begin your journey into computer architecture.</p>
                    </div>
                {/if}
            </div>
        {/if}
    </main>
</div>

<style>
	:global(body) {
		background-color: #000000;
		color: #abb2bf;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
			'Open Sans', 'Helvetica Neue', sans-serif;
		margin: 0;
	}
	main {
        /* transition: margin-left 0.3s ease-in-out; */
		display: flex;
		flex-direction: column;
		height: calc(100vh - 80px); /* Account for header height */
	}
	header {
		padding: 1rem 2rem;
		border-bottom: 1px solid #444;
	}
	h1 {
		color: #61afef;
		margin: 0;
	}
	p {
		margin: 0.5rem 0;
	}
	.controls {
		display: flex;
		gap: 1rem;
		margin-top: 1rem;
	}
	button {
		background-color: #3a3f4b;
		color: #abb2bf;
		border: 1px solid #5c6370;
		padding: 0.5rem 1rem;
		border-radius: 5px;
		cursor: pointer;
		transition: background-color 0.2s;
	}
	button:hover {
		background-color: #4b5263;
	}
	button.reset {
		border-color: #e06c75;
	}
	.workbench-container {
		flex-grow: 1;
		min-height: 0; /* Important for flexbox layout */
	}


    /* Enhanced Success Modal Styles */
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        backdrop-filter: blur(4px);
    }

    .success-modal {
        background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
        border: 2px solid #98c379;
        border-radius: 16px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        background: linear-gradient(135deg, #98c379 0%, #61afef 100%);
        padding: 2rem;
        border-radius: 14px 14px 0 0;
        text-align: center;
        color: #000;
    }

    .success-icon {
        font-size: 3rem;
        margin-bottom: 0.5rem;
        animation: bounce 0.6s ease-in-out;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .modal-header h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: bold;
        color: #000;
    }

    .modal-content {
        padding: 2rem;
        text-align: center;
    }

    .modal-content h3 {
        color: #61afef;
        margin: 0 0 1rem 0;
        font-size: 1.3rem;
    }

    .success-message {
        color: #abb2bf;
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .unlock-notification {
        background-color: #161b22;
        border: 1px solid #98c379;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .unlock-icon {
        font-size: 2rem;
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    .unlock-msg {
        color: #98c379;
        font-size: 1rem;
        margin: 0;
        text-align: left;
        line-height: 1.4;
    }

    .unlock-msg strong {
        color: #e5c07b;
    }

    .progress-indicator {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #444;
    }

    .progress-indicator p {
        color: #c678dd;
        font-style: italic;
        margin: 0;
    }

    .modal-actions {
        padding: 0 2rem 2rem 2rem;
        text-align: center;
    }

    .continue-button {
        background: linear-gradient(135deg, #61afef 0%, #98c379 100%);
        color: #000;
        border: none;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 200px;
        justify-content: center;
    }

    .continue-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(97, 175, 239, 0.3);
    }

    .button-arrow {
        transition: transform 0.3s ease;
    }

    .continue-button:hover .button-arrow {
        transform: translateX(4px);
    }

    /* Contextual Content Styles */
    .contextual-content {
        padding: 2rem;
        background-color: #000000;
        min-height: calc(100vh - 80px); /* Account for header height */
        overflow-y: auto;
    }

    .overview-content, .module-intro, .default-content {
        max-width: 1000px;
        margin: 0 auto;
    }

    .overview-content h1, .module-intro h1, .default-content h1 {
        color: #61afef;
        font-size: 2.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .journey-overview h2 {
        color: #c678dd;
        font-size: 2rem;
        margin-bottom: 1rem;
        text-align: center;
    }

    .journey-overview > p {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 3rem;
        text-align: center;
        color: #abb2bf;
    }

    .learning-goals {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .goal-item {
        background-color: #161b22;
        padding: 1.5rem;
        border-radius: 6px;
        border: 1px solid #444;
    }

    .goal-item h4 {
        color: #e5c07b;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .goal-item p {
        color: #abb2bf;
        margin: 0;
        line-height: 1.5;
    }

    .progress-summary {
        background-color: #0d1117;
        border-radius: 8px;
        padding: 2rem;
        border: 1px solid #444;
    }

    .progress-summary h3 {
        color: #c678dd;
        margin-bottom: 1.5rem;
        font-size: 1.5rem;
    }

    .progress-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1.5rem;
    }

    .stat-item {
        text-align: center;
        background-color: #161b22;
        padding: 1.5rem;
        border-radius: 6px;
        border: 1px solid #444;
    }

    .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: bold;
        color: #61afef;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #abb2bf;
        font-size: 0.9rem;
    }

    .module-description p {
        font-size: 1.2rem;
        line-height: 1.6;
        color: #abb2bf;
        margin-bottom: 2rem;
        text-align: center;
    }

    .module-learning-objectives ul {
        color: #abb2bf;
        line-height: 1.6;
        margin-left: 1.5rem;
    }

    .approach-steps {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        background-color: #161b22;
        padding: 1.5rem;
        border-radius: 6px;
        border: 1px solid #444;
    }

    .step-number {
        background-color: #61afef;
        color: #0d1117;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        flex-shrink: 0;
    }

    .step-content h3 {
        color: #e5c07b;
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
    }

    .step-content p {
        color: #abb2bf;
        margin: 0;
        line-height: 1.5;
    }

    .default-content {
        text-align: center;
        padding: 4rem 2rem;
    }

    .default-content p {
        font-size: 1.2rem;
        color: #abb2bf;
        line-height: 1.6;
    }

    /* Theory View Styles */
    .theory-view {
        padding: 2rem;
        background-color: #000000;
        min-height: calc(100vh - 80px); /* Account for header height */
        overflow-y: auto;
    }

    .theory-content {
        background-color: #0d1117;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    /* Learning Item Content Styles */
    .learning-item-content {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }

    .learning-item-content h1 {
        color: #61afef;
        font-size: 2.5rem;
        margin-bottom: 1rem;
        text-align: center;
    }

    .learning-item-content p {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        text-align: center;
        color: #abb2bf;
    }

    .video-container {
        margin: 2rem 0;
        text-align: center;
    }

    .video-container iframe {
        width: 100%;
        max-width: 800px;
        height: 450px;
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    /* Module Introduction Enhancements */
    .module-introduction {
        background-color: #0d1117;
        border-radius: 8px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid #444;
        border-left: 4px solid #61afef;
    }

    .module-introduction h2 {
        color: #61afef;
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .module-introduction p {
        color: #abb2bf;
        font-size: 1.1rem;
        line-height: 1.6;
        margin: 0;
    }



    /* Dropdown Styles */
    .dropdown-container {
        position: relative;
        display: inline-block;
    }

    .dropdown-button {
        background-color: #3a3f4b;
        color: #abb2bf;
        border: 1px solid #5c6370;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.2s;
        font-size: inherit;
        min-width: 140px;
        text-align: left;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dropdown-button:hover {
        background-color: #4b5263;
    }

    .dropdown-button:focus {
        outline: 2px solid #61afef;
        outline-offset: 2px;
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: #0d1117;
        border: 1px solid #5c6370;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
        margin-top: 2px;
    }

    .dropdown-item {
        width: 100%;
        background: none;
        border: none;
        color: #abb2bf;
        padding: 0.75rem 1rem;
        text-align: left;
        cursor: pointer;
        transition: background-color 0.2s;
        font-size: inherit;
        border-bottom: 1px solid #3a3f4b;
    }

    .dropdown-item:last-child {
        border-bottom: none;
    }

    .dropdown-item:hover {
        background-color: #3a3f4b;
    }

    .dropdown-item:focus {
        background-color: #4b5263;
        outline: none;
    }
</style>
