<script lang="ts">
	import Workbench from '$lib/components/Workbench.svelte';
    import StudyPanel from '$lib/components/StudyPanel.svelte';
    import TheoryContent from '$lib/components/TheoryContent.svelte';
    import SubsectionTheoryContent from '$lib/components/SubsectionTheoryContent.svelte';
	import { simulation, challengeStore, navigationStore } from '$lib/store';
	import type { GateType } from '$lib/logic/types';
    import { curriculum, studyContent, type Challenge } from '$lib/curriculum';
    import { onMount } from 'svelte';


    let panelWidth = 400; // Default width for the panel

    let showSuccessModal = false;
    let completedChallenge: Challenge | null = null;

    // Dropdown state
    let isDropdownOpen = false;
    let dropdownButton: HTMLButtonElement;

	let nextGateX = 50;
	let nextGateY = 50;

    // Reactively get the index and tools directly from the store's VALUE ($)
    $: levelIndex = $challengeStore.levelIndex;
    $: availableTools = $challengeStore.unlockedTools;
    $: currentView = $navigationStore.currentView;
    $: currentChallengeId = $navigationStore.currentChallengeId;
    $: currentTheoryId = $navigationStore.currentTheoryId;
    $: currentModuleId = $navigationStore.currentModuleId;
    $: currentLearningItemId = $navigationStore.currentLearningItemId;

    // Derive the current challenge from the reactive index or navigation
    $: currentChallenge = currentChallengeId
        ? curriculum.find(c => c.id === currentChallengeId) || curriculum[levelIndex]
        : curriculum[levelIndex];

    // Derive current theory content
    $: currentModule = currentModuleId ? studyContent.modules.find(m => m.id === currentModuleId) : null;
    $: currentTheory = currentTheoryId ?
        (currentModule?.theory?.id === currentTheoryId ? currentModule.theory :
         studyContent.theoryIndex.find(t => t.id === currentTheoryId)) : null;
    $: currentLearningItem = currentLearningItemId && currentModule ?
        currentModule.learningItems?.find(item => item.id === currentLearningItemId) : null;

	function add(type: GateType) {
        if (!availableTools.has(type)) return;

		simulation.addGate(type, nextGateX, nextGateY);
		nextGateX += 150;
		if (nextGateX > 500) {
			nextGateX = 50;
			nextGateY += 120;
		}
	}
    
    // Listen for challenge completion events
    function handleChallengeCompleted(event: CustomEvent<{ challenge: Challenge }>) {
        completedChallenge = event.detail.challenge;
        showSuccessModal = true;
    }

    // Set up event listener for challenge completion
    onMount(() => {
        window.addEventListener('challengeCompleted', handleChallengeCompleted);

        return () => {
            window.removeEventListener('challengeCompleted', handleChallengeCompleted);
        };
    });

    function continueAfterSuccess() {
        showSuccessModal = false;
        completedChallenge = null;

        // Navigate back to the module view to show progress
        if ($navigationStore.currentModuleId) {
            navigationStore.goToModule($navigationStore.currentModuleId);
        }
    }



    function resetWorkbench() {
        simulation.reset();
        challengeStore.clearCurrentChallengeState();
    }

    // Dropdown functionality
    function toggleDropdown() {
        isDropdownOpen = !isDropdownOpen;
    }

    function selectComponent(tool: GateType) {
        add(tool);
        isDropdownOpen = false;
    }

    function closeDropdown() {
        isDropdownOpen = false;
    }

    // Close dropdown when clicking outside
    function handleClickOutside(event: MouseEvent) {
        if (dropdownButton && !dropdownButton.contains(event.target as Node)) {
            closeDropdown();
        }
    }

    // Handle keyboard navigation
    function handleDropdownKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            closeDropdown();
            dropdownButton?.focus();
        }
    }
</script>

<!-- Add window event listener for clicking outside dropdown -->
<svelte:window on:click={handleClickOutside} />

<!-- Enhanced Success Modal -->
{#if showSuccessModal && completedChallenge}
<div class="modal-backdrop">
    <div class="success-modal">
        <div class="modal-header">
            <div class="success-icon">🎉</div>
            <h2>Challenge Complete!</h2>
        </div>

        <div class="modal-content">
            <h3>{completedChallenge.title}</h3>
            <p class="success-message">Excellent work! You've successfully completed this challenge.</p>

            {#if completedChallenge.unlocks}
                <div class="unlock-notification">
                    <div class="unlock-icon">🔓</div>
                    <p class="unlock-msg">
                        <strong>New Tool Unlocked!</strong><br>
                        You can now use the <strong>{completedChallenge.unlocks}</strong> component in future challenges.
                    </p>
                </div>
            {/if}

            <div class="progress-indicator">
                <p>Keep building your 8-bit computer, one component at a time!</p>
            </div>
        </div>

        <div class="modal-actions">
            <button class="continue-button" on:click={continueAfterSuccess}>
                Continue to Challenge Menu
                <span class="button-arrow">→</span>
            </button>
        </div>
    </div>
</div>
{/if}

<div>
    <StudyPanel
        bind:width={panelWidth}
    />
    <main style="margin-left: {panelWidth}px;">
        {#if currentView === 'learning-item' && currentLearningItem}
            {#if currentLearningItem.type === 'challenge' && currentLearningItem.challenge}
                <!-- Learning Item Challenge View -->
                <header>
                    <h1>{currentLearningItem.challenge.title}</h1>
                    <p>{currentLearningItem.challenge.description}</p>

                    <div class="controls">
                        <div class="dropdown-container">
                            <button
                                bind:this={dropdownButton}
                                class="dropdown-button"
                                on:click={toggleDropdown}
                                on:keydown={handleDropdownKeydown}
                                aria-haspopup="listbox"
                                aria-expanded={isDropdownOpen}
                                aria-label="Add component dropdown"
                            >
                                Add Component ▼
                            </button>
                            {#if isDropdownOpen}
                                <div
                                    class="dropdown-menu"
                                    role="listbox"
                                    aria-label="Available components"
                                >
                                    {#each [...availableTools] as tool (tool)}
                                        <button
                                            class="dropdown-item"
                                            role="option"
                                            aria-selected="false"
                                            on:click={() => selectComponent(tool)}
                                            on:keydown={(e) => e.key === 'Enter' && selectComponent(tool)}
                                            aria-label="Add {tool}"
                                        >
                                            {tool}
                                        </button>
                                    {/each}
                                </div>
                            {/if}
                        </div>
                        <button class="reset" on:click={resetWorkbench}>Reset Workbench</button>
                    </div>
                </header>

                <div class="workbench-container">
                    <Workbench />
                </div>
            {:else if currentLearningItem.type === 'theory'}
                <!-- Learning Item Theory View with Subsection Support -->
                <div class="theory-view">
                    <SubsectionTheoryContent learningItem={currentLearningItem} />
                </div>
            {:else}
                <!-- Other learning item types -->
                <div class="contextual-content">
                    <div class="learning-item-content">
                        <h1>{currentLearningItem.title}</h1>
                        <p>{currentLearningItem.description}</p>
                        {#if currentLearningItem.estimatedTime}
                            <p><strong>⏱️ Estimated time:</strong> {currentLearningItem.estimatedTime}</p>
                        {/if}
                        {#if currentLearningItem.type === 'video' && currentLearningItem.videoUrl}
                            <div class="video-container">
                                <iframe src={currentLearningItem.videoUrl} title={currentLearningItem.title}></iframe>
                            </div>
                        {/if}
                    </div>
                </div>
            {/if}
        {:else if currentView === 'challenge' && currentChallenge}
            <header>
                <h1>{currentChallenge.title}</h1>
                <p>{currentChallenge.description}</p>

                <div class="controls">
                    <div class="dropdown-container">
                        <button
                            bind:this={dropdownButton}
                            class="dropdown-button"
                            on:click={toggleDropdown}
                            on:keydown={handleDropdownKeydown}
                            aria-haspopup="listbox"
                            aria-expanded={isDropdownOpen}
                            aria-label="Add component dropdown"
                        >
                            Add Component ▼
                        </button>
                        {#if isDropdownOpen}
                            <div
                                class="dropdown-menu"
                                role="listbox"
                                aria-label="Available components"
                            >
                                {#each [...availableTools] as tool (tool)}
                                    <button
                                        class="dropdown-item"
                                        role="option"
                                        aria-selected="false"
                                        on:click={() => selectComponent(tool)}
                                        on:keydown={(e) => e.key === 'Enter' && selectComponent(tool)}
                                        aria-label="Add {tool}"
                                    >
                                        {tool}
                                    </button>
                                {/each}
                            </div>
                        {/if}
                    </div>
                    <button class="reset" on:click={resetWorkbench}>Reset Workbench</button>
                </div>


            </header>

            <div class="workbench-container">
                <Workbench />
            </div>
        {:else if currentView === 'theory' && currentTheory}
            <!-- Theory View - Show theory content in main area with StudyPanel visible -->
            <div class="theory-view">
                <div class="theory-content">
                    <TheoryContent theoryPath={currentTheory.content} />
                </div>
            </div>
        {:else}
            <!-- Contextual Content Area -->
            <div class="contextual-content">
                {#if currentView === 'main-menu'}
                    <!-- Learning Hub Overview -->
                    <div class="overview-content">
                        <h1>🎯 Your Learning Journey</h1>
                        <div class="journey-overview">
                            <h2>Building an 8-Bit Computer from Scratch</h2>
                            <p>You're about to embark on an incredible journey through computer architecture. Starting with basic logic gates, you'll progressively build toward a complete, programmable 8-bit computer.</p>

                            <div class="progress-summary">
                                <h3>📊 Your Progress</h3>
                                <div class="progress-stats">
                                    <div class="stat-item">
                                        <span class="stat-number">{$challengeStore.completedChallenges.size}</span>
                                        <span class="stat-label">Challenges Completed</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">{studyContent.modules.length}</span>
                                        <span class="stat-label">Modules Available</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-number">{$challengeStore.unlockedTools.size}</span>
                                        <span class="stat-label">Tools Unlocked</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {:else if currentView === 'module' && currentModule}
                    <!-- Module Introduction -->
                    <div class="module-intro">
                        <h1>📖 {currentModule.title}</h1>
                        <div class="module-description">
                            <p>{currentModule.description}</p>
                        </div>

                        {#if currentModule.introduction}
                            <div class="module-introduction">
                                <h2>👋 Welcome to This Module</h2>
                                <p>{currentModule.introduction}</p>
                            </div>
                        {/if}

                        <div class="module-learning-objectives">
                            <h2>🎯 What You'll Learn</h2>
                            <p>In this module, you will:</p>
                            <ul>
                                {#if currentModule.learningItems && currentModule.learningItems.length > 0}
                                    <li>Complete {currentModule.learningItems.filter(item => item.type === 'theory').length} theory lessons to understand the concepts</li>
                                    <li>Practice with {currentModule.learningItems.filter(item => item.type === 'challenge').length} hands-on challenges</li>
                                    <li>Build real circuits that demonstrate the principles you've learned</li>
                                {:else}
                                    <li>Study the theoretical foundations through comprehensive theory content</li>
                                    <li>Apply your knowledge through hands-on circuit building challenges</li>
                                    <li>Master the concepts needed for the next stage of computer construction</li>
                                {/if}
                            </ul>
                        </div>

                        <div class="module-approach">
                            <h2>📚 How to Proceed</h2>
                            <div class="approach-steps">
                                <div class="step-item">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h3>Follow the Learning Path</h3>
                                        <p>Work through the learning items in the StudyPanel in order for the best experience</p>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h3>Take Your Time</h3>
                                        <p>Each item has an estimated time - use this to plan your learning sessions</p>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h3>Practice and Experiment</h3>
                                        <p>Don't just complete challenges - experiment with different approaches to deepen understanding</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {:else}
                    <!-- Default fallback -->
                    <div class="default-content">
                        <h1>ByteCrafted Learning Platform</h1>
                        <p>Select a module from the Learning Hub to begin your journey into computer architecture.</p>
                    </div>
                {/if}
            </div>
        {/if}
    </main>
</div>

<style>
	:global(body) {
		background-color: #000000;
		color: #abb2bf;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
			'Open Sans', 'Helvetica Neue', sans-serif;
		margin: 0;
	}
	main {
		display: flex;
		flex-direction: column;
		height: calc(100vh - 80px); /* Account for header height */
		background-color: var(--bg-primary);
	}

	header {
		padding: var(--spacing-4) var(--spacing-6);
		border-bottom: 1px solid var(--border-default);
		background-color: var(--bg-secondary);
		box-shadow: var(--shadow-sm);
	}

	h1 {
		color: var(--color-accent);
		margin: 0;
		font-size: var(--font-size-2xl);
		font-weight: var(--font-weight-bold);
		line-height: var(--line-height-tight);
	}

	p {
		margin: var(--spacing-2) 0;
		color: var(--text-secondary);
		font-size: var(--font-size-sm);
		line-height: var(--line-height-normal);
	}

	.controls {
		display: flex;
		gap: var(--spacing-3);
		margin-top: var(--spacing-4);
		flex-wrap: wrap;
	}

	button {
		background-color: var(--bg-elevated);
		color: var(--text-primary);
		border: 1px solid var(--border-default);
		padding: var(--spacing-2) var(--spacing-4);
		border-radius: var(--radius-md);
		cursor: pointer;
		transition: all var(--transition-fast);
		font-size: var(--font-size-sm);
		font-weight: var(--font-weight-medium);
		display: flex;
		align-items: center;
		gap: var(--spacing-2);
	}

	button:hover {
		background-color: var(--bg-surface);
		border-color: var(--border-subtle);
		transform: translateY(-1px);
		box-shadow: var(--shadow-sm);
	}

	button:focus-visible {
		outline: 2px solid var(--color-accent);
		outline-offset: 2px;
	}

	button.reset {
		border-color: var(--color-error-muted);
		color: var(--color-error);
	}

	button.reset:hover {
		background-color: var(--color-error);
		color: var(--text-inverse);
		border-color: var(--color-error);
	}

	.workbench-container {
		flex-grow: 1;
		min-height: 0; /* Important for flexbox layout */
		border-top: 1px solid var(--border-default);
	}


    /* Enhanced Success Modal Styles */
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        backdrop-filter: blur(4px);
    }

    .success-modal {
        background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
        border: 2px solid #98c379;
        border-radius: 16px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        background: linear-gradient(135deg, #98c379 0%, #61afef 100%);
        padding: 2rem;
        border-radius: 14px 14px 0 0;
        text-align: center;
        color: #000;
    }

    .success-icon {
        font-size: 3rem;
        margin-bottom: 0.5rem;
        animation: bounce 0.6s ease-in-out;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .modal-header h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: bold;
        color: #000;
    }

    .modal-content {
        padding: 2rem;
        text-align: center;
    }

    .modal-content h3 {
        color: #61afef;
        margin: 0 0 1rem 0;
        font-size: 1.3rem;
    }

    .success-message {
        color: #abb2bf;
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .unlock-notification {
        background-color: #161b22;
        border: 1px solid #98c379;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .unlock-icon {
        font-size: 2rem;
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    .unlock-msg {
        color: #98c379;
        font-size: 1rem;
        margin: 0;
        text-align: left;
        line-height: 1.4;
    }

    .unlock-msg strong {
        color: #e5c07b;
    }

    .progress-indicator {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #444;
    }

    .progress-indicator p {
        color: #c678dd;
        font-style: italic;
        margin: 0;
    }

    .modal-actions {
        padding: 0 2rem 2rem 2rem;
        text-align: center;
    }

    .continue-button {
        background: linear-gradient(135deg, #61afef 0%, #98c379 100%);
        color: #000;
        border: none;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        min-width: 200px;
        justify-content: center;
    }

    .continue-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(97, 175, 239, 0.3);
    }

    .button-arrow {
        transition: transform 0.3s ease;
    }

    .continue-button:hover .button-arrow {
        transform: translateX(4px);
    }

    /* Contextual Content Styles */
    .contextual-content {
        padding: 2rem;
        background-color: #000000;
        min-height: calc(100vh - 80px); /* Account for header height */
        overflow-y: auto;
    }

    .overview-content, .module-intro, .default-content {
        max-width: 1000px;
        margin: 0 auto;
    }

    .overview-content h1, .module-intro h1, .default-content h1 {
        color: #61afef;
        font-size: 2.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .journey-overview h2 {
        color: #c678dd;
        font-size: 2rem;
        margin-bottom: 1rem;
        text-align: center;
    }

    .journey-overview > p {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 3rem;
        text-align: center;
        color: #abb2bf;
    }

    .learning-goals {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .goal-item {
        background-color: #161b22;
        padding: 1.5rem;
        border-radius: 6px;
        border: 1px solid #444;
    }

    .goal-item h4 {
        color: #e5c07b;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .goal-item p {
        color: #abb2bf;
        margin: 0;
        line-height: 1.5;
    }

    .progress-summary {
        background-color: #0d1117;
        border-radius: 8px;
        padding: 2rem;
        border: 1px solid #444;
    }

    .progress-summary h3 {
        color: #c678dd;
        margin-bottom: 1.5rem;
        font-size: 1.5rem;
    }

    .progress-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1.5rem;
    }

    .stat-item {
        text-align: center;
        background-color: #161b22;
        padding: 1.5rem;
        border-radius: 6px;
        border: 1px solid #444;
    }

    .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: bold;
        color: #61afef;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #abb2bf;
        font-size: 0.9rem;
    }

    .module-description p {
        font-size: 1.2rem;
        line-height: 1.6;
        color: #abb2bf;
        margin-bottom: 2rem;
        text-align: center;
    }

    .module-learning-objectives ul {
        color: #abb2bf;
        line-height: 1.6;
        margin-left: 1.5rem;
    }

    .approach-steps {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        background-color: #161b22;
        padding: 1.5rem;
        border-radius: 6px;
        border: 1px solid #444;
    }

    .step-number {
        background-color: #61afef;
        color: #0d1117;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        flex-shrink: 0;
    }

    .step-content h3 {
        color: #e5c07b;
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
    }

    .step-content p {
        color: #abb2bf;
        margin: 0;
        line-height: 1.5;
    }

    .default-content {
        text-align: center;
        padding: 4rem 2rem;
    }

    .default-content p {
        font-size: 1.2rem;
        color: #abb2bf;
        line-height: 1.6;
    }

    /* Theory View Styles */
    .theory-view {
        padding: 2rem;
        background-color: #000000;
        min-height: calc(100vh - 80px); /* Account for header height */
        overflow-y: auto;
    }

    .theory-content {
        background-color: #0d1117;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    /* Learning Item Content Styles */
    .learning-item-content {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
    }

    .learning-item-content h1 {
        color: #61afef;
        font-size: 2.5rem;
        margin-bottom: 1rem;
        text-align: center;
    }

    .learning-item-content p {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        text-align: center;
        color: #abb2bf;
    }

    .video-container {
        margin: 2rem 0;
        text-align: center;
    }

    .video-container iframe {
        width: 100%;
        max-width: 800px;
        height: 450px;
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    /* Module Introduction Enhancements */
    .module-introduction {
        background-color: #0d1117;
        border-radius: 8px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid #444;
        border-left: 4px solid #61afef;
    }

    .module-introduction h2 {
        color: #61afef;
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .module-introduction p {
        color: #abb2bf;
        font-size: 1.1rem;
        line-height: 1.6;
        margin: 0;
    }



    /* Dropdown Styles */
    .dropdown-container {
        position: relative;
        display: inline-block;
    }

    .dropdown-button {
        background-color: var(--bg-elevated);
        color: var(--text-primary);
        border: 1px solid var(--border-default);
        padding: var(--spacing-2) var(--spacing-4);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        min-width: 160px;
        text-align: left;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: var(--spacing-2);
    }

    .dropdown-button:hover {
        background-color: var(--bg-surface);
        border-color: var(--border-subtle);
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    .dropdown-button:focus {
        outline: 2px solid var(--color-accent);
        outline-offset: 2px;
    }

    .dropdown-menu {
        position: absolute;
        top: calc(100% + var(--spacing-1));
        left: 0;
        right: 0;
        background-color: var(--bg-card);
        border: 1px solid var(--border-default);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-xl);
        z-index: var(--z-dropdown);
        max-height: 240px;
        overflow-y: auto;
        backdrop-filter: blur(8px);
        animation: dropdownSlideIn 0.2s ease-out;
    }

    @keyframes dropdownSlideIn {
        from {
            opacity: 0;
            transform: translateY(-8px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .dropdown-item {
        width: 100%;
        background: none;
        border: none;
        color: var(--text-primary);
        padding: var(--spacing-3) var(--spacing-4);
        text-align: left;
        cursor: pointer;
        transition: all var(--transition-fast);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-normal);
        border-bottom: 1px solid var(--border-muted);
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
    }

    .dropdown-item:first-child {
        border-top-left-radius: var(--radius-lg);
        border-top-right-radius: var(--radius-lg);
    }

    .dropdown-item:last-child {
        border-bottom: none;
        border-bottom-left-radius: var(--radius-lg);
        border-bottom-right-radius: var(--radius-lg);
    }

    .dropdown-item:hover {
        background-color: var(--bg-surface);
        color: var(--color-accent);
    }

    .dropdown-item:focus {
        background-color: var(--bg-surface);
        color: var(--color-accent);
        outline: none;
    }
</style>
