<script lang="ts">
  import { goto } from '$app/navigation';

  function startLearning() {
    goto('/learn');
  }
</script>

<div class="home-container">
  <div class="hero-section">
    <div class="hero-content">
      <h1 class="hero-title">
        <span class="brand">ByteCrafted</span>
        <span class="subtitle">Build an 8-Bit Computer from Scratch</span>
      </h1>
      
      <p class="hero-description">
        Master computer architecture through hands-on construction. Start with basic logic gates 
        and progressively build toward a complete, programmable 8-bit computer running custom code.
      </p>

      <button class="cta-button" on:click={startLearning}>
        Start Building Your Computer
        <span class="cta-arrow">→</span>
      </button>

      <div class="journey-preview">
        <div class="journey-step">
          <div class="step-icon">⚡</div>
          <div class="step-content">
            <h3>Logic Gates</h3>
            <p>Build AND, OR, XOR from NAND gates</p>
          </div>
        </div>
        
        <div class="journey-arrow">→</div>
        
        <div class="journey-step">
          <div class="step-icon">🔢</div>
          <div class="step-content">
            <h3>Arithmetic Circuits</h3>
            <p>Create adders and ALU components</p>
          </div>
        </div>
        
        <div class="journey-arrow">→</div>
        
        <div class="journey-step">
          <div class="step-icon">💾</div>
          <div class="step-content">
            <h3>Memory Systems</h3>
            <p>Build registers, RAM, and storage</p>
          </div>
        </div>
        
        <div class="journey-arrow">→</div>
        
        <div class="journey-step">
          <div class="step-icon">🖥️</div>
          <div class="step-content">
            <h3>Complete Computer</h3>
            <p>Assemble your programmable 8-bit system</p>
          </div>
        </div>
      </div>

      <div class="features">
        <div class="feature">
          <h4>🛠️ Learning by Building</h4>
          <p>Construct each component from previously mastered parts</p>
        </div>
        <div class="feature">
          <h4>🎯 First Principles</h4>
          <p>Understand how computers work from the ground up</p>
        </div>
        <div class="feature">
          <h4>💻 Interactive Workbench</h4>
          <p>Visual circuit builder with real-time simulation</p>
        </div>
      </div>
    </div>
  </div>

  <div class="background-pattern"></div>
</div>

<style>
  .home-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #000000 0%, #0d1117 50%, #161b22 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }

  .background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(97, 175, 239, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(152, 195, 121, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  .hero-section {
    max-width: 1200px;
    padding: 2rem;
    text-align: center;
    position: relative;
    z-index: 1;
  }

  .hero-title {
    margin-bottom: 2rem;
  }

  .brand {
    display: block;
    font-size: 4rem;
    font-weight: bold;
    background: linear-gradient(45deg, #61afef, #c678dd, #98c379);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
  }

  .subtitle {
    display: block;
    font-size: 1.8rem;
    color: #abb2bf;
    font-weight: 300;
  }

  .hero-description {
    font-size: 1.3rem;
    color: #abb2bf;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .journey-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
  }

  .journey-step {
    background: rgba(13, 17, 23, 0.8);
    border: 1px solid #444;
    border-radius: 12px;
    padding: 1.5rem;
    min-width: 180px;
    transition: all 0.3s ease;
  }

  .journey-step:hover {
    transform: translateY(-5px);
    border-color: #61afef;
    box-shadow: 0 8px 25px rgba(97, 175, 239, 0.2);
  }

  .step-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .step-content h3 {
    color: #61afef;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
  }

  .step-content p {
    color: #abb2bf;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .journey-arrow {
    color: #5c6370;
    font-size: 1.5rem;
    font-weight: bold;
  }

  .cta-button {
    background: linear-gradient(45deg, #61afef, #98c379);
    color: #282c34;
    border: none;
    padding: 1.2rem 2.5rem;
    font-size: 1.3rem;
    font-weight: bold;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 3rem;
  }

  .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(97, 175, 239, 0.4);
  }

  .cta-arrow {
    transition: transform 0.3s ease;
  }

  .cta-button:hover .cta-arrow {
    transform: translateX(5px);
  }

  .features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
  }

  .feature {
    text-align: left;
    background: rgba(13, 17, 23, 0.6);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #444;
  }

  .feature h4 {
    color: #e5c07b;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
  }

  .feature p {
    color: #abb2bf;
    margin: 0;
    line-height: 1.5;
  }

  @media (max-width: 768px) {
    .brand {
      font-size: 3rem;
    }
    
    .subtitle {
      font-size: 1.4rem;
    }
    
    .hero-description {
      font-size: 1.1rem;
    }
    
    .journey-preview {
      flex-direction: column;
    }
    
    .journey-arrow {
      transform: rotate(90deg);
    }
    
    .features {
      grid-template-columns: 1fr;
    }
  }
</style>
