/* ByteCrafted Global Styles - Professional Design System */

/* Import Professional Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* CSS Custom Properties for consistent theming */
:root {
  /* Enhanced Color Palette - Professional dark theme with improved contrast */
  --bg-primary: #000000;
  --bg-secondary: #0d1117;
  --bg-tertiary: #161b22;
  --bg-elevated: #21262d;
  --bg-surface: #2d333b;
  --bg-overlay: #373e47;
  --bg-card: #1c2128;

  /* Text Colors with improved contrast ratios */
  --text-primary: #f0f6fc;
  --text-secondary: #8b949e;
  --text-tertiary: #6e7681;
  --text-muted: #484f58;
  --text-inverse: #0d1117;

  /* Brand Colors */
  --color-accent: #58a6ff;
  --color-accent-hover: #79c0ff;
  --color-accent-muted: #1f6feb;

  /* Semantic Colors */
  --color-success: #3fb950;
  --color-success-hover: #56d364;
  --color-success-muted: #238636;

  --color-warning: #d29922;
  --color-warning-hover: #e3b341;
  --color-warning-muted: #9e6a03;

  --color-error: #f85149;
  --color-error-hover: #ff7b72;
  --color-error-muted: #da3633;

  --color-info: #79c0ff;
  --color-info-hover: #a5d6ff;
  --color-info-muted: #1f6feb;

  /* Legacy color mappings for backward compatibility */
  --text-accent: var(--color-accent);
  --text-warning: var(--color-warning);
  --text-error: var(--color-error);
  --text-success: var(--color-success);
  --text-purple: #bc8cff;

  /* Border Colors */
  --border-default: #30363d;
  --border-muted: #21262d;
  --border-subtle: #373e47;
  --border-color: var(--border-default); /* Legacy compatibility */
  --border-light: var(--border-subtle); /* Legacy compatibility */

  /* Enhanced Spacing Scale */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */

  /* Legacy spacing for backward compatibility */
  --spacing-xs: var(--spacing-1);
  --spacing-sm: var(--spacing-2);
  --spacing-md: var(--spacing-4);
  --spacing-lg: var(--spacing-6);
  --spacing-xl: var(--spacing-8);
  --spacing-2xl: var(--spacing-12);

  /* Professional Typography Scale */
  --font-family-sans:
    "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans",
    Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  --font-family-mono:
    "JetBrains Mono", "SF Mono", Monaco, "Cascadia Code", "Roboto Mono",
    Consolas, "Courier New", monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Enhanced Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Professional Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Enhanced Border Radius */
  --radius-none: 0;
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-3xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-family-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography System */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 var(--spacing-4) 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent);
  line-height: var(--line-height-tight);
}

h2 {
  font-size: var(--font-size-3xl);
  color: var(--text-purple);
  line-height: var(--line-height-tight);
}

h3 {
  font-size: var(--font-size-2xl);
  color: var(--color-warning);
  line-height: var(--line-height-snug);
}

h4 {
  font-size: var(--font-size-xl);
  color: var(--text-primary);
  line-height: var(--line-height-snug);
}

h5 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
}

h6 {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

p {
  margin: 0 0 var(--spacing-4) 0;
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
}

/* Text Size Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

/* Font Weight Utilities */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Line Height Utilities */
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

a {
  color: var(--text-accent);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--text-success);
  text-decoration: underline;
}

/* Code and Preformatted Text */
code {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}

pre {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  overflow-x: auto;
  border: 1px solid var(--border-color);
}

pre code {
  background: none;
  padding: 0;
}

/* Enhanced Button System */
button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-default);
  background-color: var(--bg-elevated);
  color: var(--text-primary);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

button:hover {
  background-color: var(--bg-surface);
  border-color: var(--border-subtle);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-xs);
}

button:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Button Variants */
button.primary {
  background: linear-gradient(135deg, var(--color-accent), var(--color-accent-hover));
  color: var(--text-inverse);
  border: none;
  font-weight: var(--font-weight-semibold);
}

button.primary:hover {
  background: linear-gradient(135deg, var(--color-accent-hover), var(--color-accent));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

button.secondary {
  background-color: transparent;
  border-color: var(--color-accent);
  color: var(--color-accent);
}

button.secondary:hover {
  background-color: var(--color-accent);
  color: var(--text-inverse);
}

button.success {
  background-color: var(--color-success);
  color: var(--text-inverse);
  border: none;
}

button.success:hover {
  background-color: var(--color-success-hover);
}

button.warning {
  background-color: var(--color-warning);
  color: var(--text-inverse);
  border: none;
}

button.warning:hover {
  background-color: var(--color-warning-hover);
}

button.danger {
  background-color: var(--color-error);
  color: var(--text-inverse);
  border: none;
}

button.danger:hover {
  background-color: var(--color-error-hover);
}

button.ghost {
  background-color: transparent;
  border: none;
  color: var(--text-secondary);
}

button.ghost:hover {
  background-color: var(--bg-surface);
  color: var(--text-primary);
}

/* Button Sizes */
button.sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
}

button.lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
}

button.xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

/* Enhanced Form Elements */
input,
textarea,
select {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-default);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  transition: all var(--transition-fast);
}

input:hover,
textarea:hover,
select:hover {
  border-color: var(--border-subtle);
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
  background-color: var(--bg-elevated);
}

input:disabled,
textarea:disabled,
select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--bg-muted);
}

input::placeholder,
textarea::placeholder {
  color: var(--text-tertiary);
}

/* Input Sizes */
input.sm,
textarea.sm,
select.sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
}

input.lg,
textarea.lg,
select.lg {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--font-size-base);
}

/* Professional Card and Surface Components */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-default);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-subtle);
}

.card-header {
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--border-muted);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-description {
  margin: var(--spacing-2) 0 0 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.card-content {
  margin-bottom: var(--spacing-4);
}

.card-footer {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-muted);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

/* Surface Variants */
.surface {
  background-color: var(--bg-elevated);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
}

.surface-raised {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-default);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
}

.surface-inset {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-inner);
}

/* Enhanced Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Color Utilities */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--color-accent); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

/* Background Utilities */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-elevated { background-color: var(--bg-elevated); }
.bg-surface { background-color: var(--bg-surface); }
.bg-card { background-color: var(--bg-card); }

/* Border Utilities */
.border { border: 1px solid var(--border-default); }
.border-muted { border: 1px solid var(--border-muted); }
.border-subtle { border: 1px solid var(--border-subtle); }

/* Border Radius Utilities */
.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }
.shadow-none { box-shadow: none; }

/* Transition Utilities */
.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* Enhanced Micro-interactions */
.hover-lift {
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: box-shadow var(--transition-fast);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(88, 166, 255, 0.3);
}

/* Focus Animations */
@keyframes focusRing {
  0% {
    box-shadow: 0 0 0 0 rgba(88, 166, 255, 0.4);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(88, 166, 255, 0);
  }
}

.focus-ring:focus-visible {
  animation: focusRing 0.6s ease-out;
}

/* Loading Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Slide Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in-up {
  animation: slideInUp 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .card {
    padding: var(--spacing-4);
  }

  .surface-raised {
    padding: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: var(--font-size-3xl);
  }
  h2 {
    font-size: var(--font-size-2xl);
  }
  h3 {
    font-size: var(--font-size-xl);
  }
  h4 {
    font-size: var(--font-size-lg);
  }

  .card {
    padding: var(--spacing-4);
    border-radius: var(--radius-md);
  }

  .card-footer {
    flex-direction: column;
  }

  button.lg {
    padding: var(--spacing-3) var(--spacing-5);
  }

  button.xl {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: var(--font-size-2xl);
  }
  h2 {
    font-size: var(--font-size-xl);
  }

  .card {
    padding: var(--spacing-3);
    margin: var(--spacing-2);
  }

  button {
    width: 100%;
    justify-content: center;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Selection */
::selection {
  background-color: var(--text-accent);
  color: var(--bg-primary);
}

::-moz-selection {
  background-color: var(--text-accent);
  color: var(--bg-primary);
}
