/* ByteCrafted Global Styles */

/* CSS Custom Properties for consistent theming */
:root {
  /* Color Palette - Modern dark theme with black primary background */
  --bg-primary: #000000;
  --bg-secondary: #0d1117;
  --bg-tertiary: #161b22;
  --bg-elevated: #21262d;

  --text-primary: #abb2bf;
  --text-secondary: #5c6370;
  --text-accent: #61afef;
  --text-warning: #e5c07b;
  --text-error: #e06c75;
  --text-success: #98c379;
  --text-purple: #c678dd;

  --border-color: #444;
  --border-light: #5c6370;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-family-sans:
    -apple-system, BlinkMacSystemFont, "<PERSON><PERSON><PERSON> UI", <PERSON><PERSON>, <PERSON>xygen, <PERSON>buntu,
    Can<PERSON>ell, "Open Sans", "Helvetica Neue", sans-serif;
  --font-family-mono:
    "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New",
    monospace;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-family-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 var(--spacing-md) 0;
  font-weight: 600;
  line-height: 1.3;
}

h1 {
  font-size: 2.5rem;
  color: var(--text-accent);
}

h2 {
  font-size: 2rem;
  color: var(--text-purple);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-warning);
}

h4 {
  font-size: 1.25rem;
  color: var(--text-primary);
}

p {
  margin: 0 0 var(--spacing-md) 0;
}

a {
  color: var(--text-accent);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--text-success);
  text-decoration: underline;
}

/* Code and Preformatted Text */
code {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}

pre {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  overflow-x: auto;
  border: 1px solid var(--border-color);
}

pre code {
  background: none;
  padding: 0;
}

/* Form Elements */
button {
  background-color: var(--bg-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-family: inherit;
  font-size: inherit;
}

button:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--text-accent);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button.primary {
  background: linear-gradient(45deg, var(--text-accent), var(--text-success));
  color: var(--bg-secondary);
  border: none;
  font-weight: 600;
}

button.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button.danger {
  border-color: var(--text-error);
  color: var(--text-error);
}

button.danger:hover {
  background-color: var(--text-error);
  color: var(--bg-primary);
}

input,
textarea,
select {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-family: inherit;
  font-size: inherit;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--text-accent);
  box-shadow: 0 0 0 2px rgba(97, 175, 239, 0.2);
}

/* Utility Classes */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.text-primary {
  color: var(--text-primary);
}
.text-secondary {
  color: var(--text-secondary);
}
.text-accent {
  color: var(--text-accent);
}
.text-warning {
  color: var(--text-warning);
}
.text-error {
  color: var(--text-error);
}
.text-success {
  color: var(--text-success);
}

.bg-primary {
  background-color: var(--bg-primary);
}
.bg-secondary {
  background-color: var(--bg-secondary);
}
.bg-elevated {
  background-color: var(--bg-elevated);
}

.border {
  border: 1px solid var(--border-color);
}
.border-radius {
  border-radius: var(--radius-md);
}

.shadow {
  box-shadow: var(--shadow-md);
}

.transition {
  transition: all var(--transition-normal);
}

/* Responsive Design */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }
  h2 {
    font-size: 1.75rem;
  }
  h3 {
    font-size: 1.5rem;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Selection */
::selection {
  background-color: var(--text-accent);
  color: var(--bg-primary);
}

::-moz-selection {
  background-color: var(--text-accent);
  color: var(--bg-primary);
}
